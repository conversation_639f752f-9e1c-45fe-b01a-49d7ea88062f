<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian Wraps & Signs - Premium Vehicle Wraps & Custom Signage</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap');
        * { font-family: 'Inter', sans-serif; }
        html { scroll-behavior: smooth; }
        body { background: #000000; }
        .animate-fade-in { animation: fadeIn 0.6s ease-out forwards; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        ::-webkit-scrollbar { width: 10px; }
        ::-webkit-scrollbar-track { background: #1a1a1a; }
        ::-webkit-scrollbar-thumb { background: #dc2626; border-radius: 5px; }
        ::-webkit-scrollbar-thumb:hover { background: #b91c1c; }
    </style>
</head>
<body class="bg-black">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-black/95 backdrop-blur-sm z-50 border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <h1 class="text-2xl font-bold text-white"><span class="text-[#dc2626]">OBSIDIAN</span> WRAPS & SIGNS</h1>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-300 hover:text-[#dc2626] transition-colors font-medium">Home</a>
                    <a href="#services" class="text-gray-300 hover:text-[#dc2626] transition-colors font-medium">Services</a>
                    <a href="#portfolio" class="text-gray-300 hover:text-[#dc2626] transition-colors font-medium">Portfolio</a>
                    <a href="#testimonials" class="text-gray-300 hover:text-[#dc2626] transition-colors font-medium">Testimonials</a>
                    <a href="#contact" class="bg-[#dc2626] text-white px-6 py-3 rounded-full font-semibold hover:bg-[#b91c1c] transition-all shadow-lg">Get Quote</a>
                </div>
                <button id="mobile-menu-btn" class="md:hidden text-gray-300 hover:text-[#dc2626]">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div id="mobile-menu" class="hidden md:hidden border-t border-gray-800 pb-4">
                <a href="#home" class="block px-4 py-2 text-gray-300 hover:text-[#dc2626]">Home</a>
                <a href="#services" class="block px-4 py-2 text-gray-300 hover:text-[#dc2626]">Services</a>
                <a href="#portfolio" class="block px-4 py-2 text-gray-300 hover:text-[#dc2626]">Portfolio</a>
                <a href="#testimonials" class="block px-4 py-2 text-gray-300 hover:text-[#dc2626]">Testimonials</a>
                <a href="#contact" class="block mx-4 my-2 px-4 py-2 bg-[#dc2626] text-white rounded-full text-center font-semibold">Get Quote</a>
            </div>
        </div>
    </nav>

    <!-- Image Gallery with Contact Overlay -->
    <section class="relative h-96 bg-gray-200 overflow-hidden">
        <!-- Background Images Grid -->
        <div class="absolute inset-0 grid grid-cols-3 gap-1">
            <div class="bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500&h=400&fit=crop')"></div>
            <div class="bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=500&h=400&fit=crop')"></div>
            <div class="bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=500&h=400&fit=crop')"></div>
        </div>

        <!-- Semi-transparent Contact Overlay -->
        <div class="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center">
            <div class="text-center text-white px-6 py-8 bg-black bg-opacity-40 rounded-2xl backdrop-blur-sm border border-white border-opacity-20 max-w-md mx-4">
                <h3 class="text-3xl font-bold mb-4">Ready to Transform Your Brand?</h3>
                <p class="text-lg mb-6 text-gray-200">Get a free quote for your vehicle wrap or custom signage project today.</p>
                <div class="space-y-3">
                    <a href="#contact" class="block bg-[#dc2626] text-white px-8 py-3 rounded-full font-bold text-lg hover:bg-[#b91c1c] transition-all shadow-lg">
                        Get Free Quote
                    </a>
                    <div class="flex items-center justify-center gap-6 text-sm">
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <span>(*************</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <span>Quick Response</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Hero -->
    <section id="home" class="pt-32 pb-20 px-4 bg-black">
        <div class="max-w-7xl mx-auto grid md:grid-cols-2 gap-12 items-center">
            <div class="space-y-8 animate-fade-in">
                <h2 class="text-5xl md:text-6xl font-bold text-white leading-tight">Transform Your Brand with<span class="block text-[#dc2626]">Premium Wraps & Signs</span></h2>
                <p class="text-xl text-gray-300">We create stunning vehicle wraps and custom signage that make your business impossible to ignore.</p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="#contact" class="bg-[#dc2626] text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-[#b91c1c] transition-all shadow-lg text-center">Get Free Quote</a>
                    <a href="#portfolio" class="bg-gray-800 text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-700 transition-all border-2 border-gray-700 text-center">View Our Work</a>
                </div>
                <div class="flex items-center gap-8 pt-4">
                    <div><div class="text-3xl font-bold text-white">25+</div><div class="text-sm text-gray-400">Years</div></div>
                    <div class="h-12 w-px bg-gray-700"></div>
                    <div><div class="text-3xl font-bold text-white">1000+</div><div class="text-sm text-gray-400">Projects</div></div>
                    <div class="h-12 w-px bg-gray-700"></div>
                    <div><div class="text-3xl font-bold text-white">100%</div><div class="text-sm text-gray-400">Satisfaction</div></div>
                </div>
            </div>
            <div class="relative h-[500px] rounded-2xl overflow-hidden shadow-2xl border-2 border-gray-800">
                <img src="https://images.unsplash.com/photo-1619405399517-d7fce0f13302?w=1200&q=80" alt="Vehicle Wrap" class="w-full h-full object-cover">
            </div>
        </div>
    </section>

    <!-- Services -->
    <section id="services" class="py-20 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-black mb-4">Our Services</h2>
                <p class="text-xl text-gray-700">Quality signage and wraps at competitive prices</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-300 hover:border-[#dc2626] transition-all duration-300 hover:shadow-xl hover:shadow-red-900/20">
                    <div class="text-5xl mb-4">🚗</div>
                    <h3 class="text-2xl font-bold text-black mb-3 group-hover:text-[#dc2626] transition-colors">Vehicle Wraps</h3>
                    <p class="text-gray-600">Transform your vehicle into a mobile billboard with our premium vinyl wraps.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-300 hover:border-[#dc2626] transition-all duration-300 hover:shadow-xl hover:shadow-red-900/20">
                    <div class="text-5xl mb-4">🪧</div>
                    <h3 class="text-2xl font-bold text-black mb-3 group-hover:text-[#dc2626] transition-colors">Custom Signage</h3>
                    <p class="text-gray-600">Eye-catching signs that make your business stand out from the competition.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-300 hover:border-[#dc2626] transition-all duration-300 hover:shadow-xl hover:shadow-red-900/20">
                    <div class="text-5xl mb-4">🏪</div>
                    <h3 class="text-2xl font-bold text-black mb-3 group-hover:text-[#dc2626] transition-colors">Shop Signage</h3>
                    <p class="text-gray-600">Professional storefront signs that attract customers and build your brand.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-300 hover:border-[#dc2626] transition-all duration-300 hover:shadow-xl hover:shadow-red-900/20">
                    <div class="text-5xl mb-4">🏢</div>
                    <h3 class="text-2xl font-bold text-black mb-3 group-hover:text-[#dc2626] transition-colors">Building Wraps</h3>
                    <p class="text-gray-600">Large-scale building wraps for maximum visibility and impact.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-300 hover:border-[#dc2626] transition-all duration-300 hover:shadow-xl hover:shadow-red-900/20">
                    <div class="text-5xl mb-4">🪟</div>
                    <h3 class="text-2xl font-bold text-black mb-3 group-hover:text-[#dc2626] transition-colors">Window Graphics</h3>
                    <p class="text-gray-600">Frosted, printed, or cut vinyl graphics for windows and glass surfaces.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-300 hover:border-[#dc2626] transition-all duration-300 hover:shadow-xl hover:shadow-red-900/20">
                    <div class="text-5xl mb-4">🚚</div>
                    <h3 class="text-2xl font-bold text-black mb-3 group-hover:text-[#dc2626] transition-colors">Fleet Branding</h3>
                    <p class="text-gray-600">Consistent branding across your entire vehicle fleet.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio -->
    <section id="portfolio" class="py-20 px-4 bg-black">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-4">Our Work</h2>
                <p class="text-xl text-gray-300 mb-8">Recent projects that showcase our expertise</p>
                <div class="flex flex-wrap justify-center gap-4 mb-8">
                    <button class="filter-btn px-6 py-2 rounded-full font-semibold bg-[#dc2626] text-white" data-filter="all">All</button>
                    <button class="filter-btn px-6 py-2 rounded-full font-semibold bg-gray-800 text-gray-300 hover:bg-gray-700" data-filter="vehicle">Vehicle Wraps</button>
                    <button class="filter-btn px-6 py-2 rounded-full font-semibold bg-gray-800 text-gray-300 hover:bg-gray-700" data-filter="signage">Signage</button>
                    <button class="filter-btn px-6 py-2 rounded-full font-semibold bg-gray-800 text-gray-300 hover:bg-gray-700" data-filter="building">Building Wraps</button>
                </div>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6" id="portfolio-grid">
                <div class="portfolio-item group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300" data-category="vehicle">
                    <img src="https://images.unsplash.com/photo-1619405399517-d7fce0f13302?w=800&q=80" alt="Project" class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-white text-2xl font-bold mb-2">Luxury Car Wrap</h3>
                            <span class="inline-block bg-[#dc2626] text-white px-3 py-1 rounded-full text-sm font-semibold">Vehicle</span>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300" data-category="signage">
                    <img src="https://images.unsplash.com/photo-1556228578-0d85b1a4d571?w=800&q=80" alt="Project" class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-white text-2xl font-bold mb-2">Retail Storefront</h3>
                            <span class="inline-block bg-[#dc2626] text-white px-3 py-1 rounded-full text-sm font-semibold">Signage</span>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300" data-category="vehicle">
                    <img src="https://images.unsplash.com/photo-1527786356703-4b100091cd2c?w=800&q=80" alt="Project" class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-white text-2xl font-bold mb-2">Commercial Van</h3>
                            <span class="inline-block bg-[#dc2626] text-white px-3 py-1 rounded-full text-sm font-semibold">Vehicle</span>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300" data-category="building">
                    <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&q=80" alt="Project" class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-white text-2xl font-bold mb-2">Office Building Wrap</h3>
                            <span class="inline-block bg-[#dc2626] text-white px-3 py-1 rounded-full text-sm font-semibold">Building</span>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300" data-category="signage">
                    <img src="https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=800&q=80" alt="Project" class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-white text-2xl font-bold mb-2">LED Shop Sign</h3>
                            <span class="inline-block bg-[#dc2626] text-white px-3 py-1 rounded-full text-sm font-semibold">Signage</span>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300" data-category="vehicle">
                    <img src="https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?w=800&q=80" alt="Project" class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/90 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-white text-2xl font-bold mb-2">Delivery Fleet</h3>
                            <span class="inline-block bg-[#dc2626] text-white px-3 py-1 rounded-full text-sm font-semibold">Vehicle</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section id="testimonials" class="py-20 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-black mb-4">What Our Clients Say</h2>
                <p class="text-xl text-gray-700">Don't just take our word for it</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-2xl border-2 border-gray-300 shadow-lg">
                    <div class="flex mb-4">
                        <span class="text-[#dc2626]">★★★★★</span>
                    </div>
                    <p class="text-gray-700 mb-6">"Absolutely incredible work! Our fleet looks amazing and we've seen a huge increase in brand recognition."</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-300 rounded-full mr-4"></div>
                        <div>
                            <div class="font-bold text-black">Sarah Johnson</div>
                            <div class="text-sm text-gray-600">CEO, FastDelivery Co</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-2xl border-2 border-gray-300 shadow-lg">
                    <div class="flex mb-4">
                        <span class="text-[#dc2626]">★★★★★</span>
                    </div>
                    <p class="text-gray-700 mb-6">"Professional, fast, and the quality is outstanding. Our new storefront sign has brought in so many new customers!"</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-300 rounded-full mr-4"></div>
                        <div>
                            <div class="font-bold text-black">Mike Chen</div>
                            <div class="text-sm text-gray-600">Owner, Chen's Restaurant</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-2xl border-2 border-gray-300 shadow-lg">
                    <div class="flex mb-4">
                        <span class="text-[#dc2626]">★★★★★</span>
                    </div>
                    <p class="text-gray-700 mb-6">"Best investment we've made for our business. The vehicle wrap pays for itself in advertising value!"</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-300 rounded-full mr-4"></div>
                        <div>
                            <div class="font-bold text-black">Emma Davis</div>
                            <div class="text-sm text-gray-600">Marketing Director, TechStart</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact -->
    <section id="contact" class="py-20 px-4 bg-black">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-4">Get Your Free Quote</h2>
                <p class="text-xl text-gray-300">Ready to transform your brand? Let's talk!</p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-8 mb-12">
                <div class="flex items-center gap-4 p-6 bg-white rounded-xl border border-gray-300 shadow-lg">
                    <div class="bg-[#dc2626] p-4 rounded-full">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                    </div>
                    <div>
                        <div class="font-bold text-black text-lg">Phone</div>
                        <div class="text-gray-700">(*************</div>
                    </div>
                </div>
                <div class="flex items-center gap-4 p-6 bg-white rounded-xl border border-gray-300 shadow-lg">
                    <div class="bg-[#dc2626] p-4 rounded-full">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div>
                        <div class="font-bold text-black text-lg">Email</div>
                        <div class="text-gray-700"><EMAIL></div>
                    </div>
                </div>
            </div>

            <div class="bg-white border-2 border-gray-300 p-8 rounded-2xl shadow-lg">
                <form class="space-y-6">
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-black mb-2">Full Name</label>
                            <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 bg-white text-black focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none placeholder-gray-500" placeholder="John Doe">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-black mb-2">Email</label>
                            <input type="email" class="w-full px-4 py-3 rounded-lg border border-gray-300 bg-white text-black focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none placeholder-gray-500" placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-black mb-2">Phone</label>
                            <input type="tel" class="w-full px-4 py-3 rounded-lg border border-gray-300 bg-white text-black focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none placeholder-gray-500" placeholder="(*************">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-black mb-2">Service</label>
                            <select class="w-full px-4 py-3 rounded-lg border border-gray-300 bg-white text-black focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none">
                                <option>Vehicle Wraps</option>
                                <option>Custom Signage</option>
                                <option>Shop Signage</option>
                                <option>Building Wraps</option>
                                <option>Window Graphics</option>
                                <option>Fleet Branding</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-semibold text-black mb-2">Project Details</label>
                        <textarea rows="4" class="w-full px-4 py-3 rounded-lg border border-gray-300 bg-white text-black focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none resize-none placeholder-gray-500" placeholder="Tell us about your project..."></textarea>
                    </div>
                    <button type="submit" class="w-full bg-[#dc2626] text-white py-4 rounded-lg font-bold text-lg hover:bg-[#b91c1c] transition-all shadow-lg hover:shadow-xl">
                        Send Message
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black text-white py-12 px-4 border-t border-gray-800">
        <div class="max-w-7xl mx-auto">
            <div class="grid md:grid-cols-4 gap-8 mb-8">
                <div class="md:col-span-2">
                    <h3 class="text-2xl font-bold mb-4"><span class="text-[#dc2626]">OBSIDIAN</span> WRAPS & SIGNS</h3>
                    <p class="text-gray-400 mb-4">Premium vehicle wraps and custom signage solutions that transform your brand and drive results.</p>
                </div>
                <div>
                    <h4 class="font-bold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-400 hover:text-[#dc2626] transition-colors">Home</a></li>
                        <li><a href="#services" class="text-gray-400 hover:text-[#dc2626] transition-colors">Services</a></li>
                        <li><a href="#portfolio" class="text-gray-400 hover:text-[#dc2626] transition-colors">Portfolio</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-[#dc2626] transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold mb-4">Follow Us</h4>
                    <div class="flex gap-4">
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-[#dc2626] transition-colors">
                            <span class="text-white">f</span>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-[#dc2626] transition-colors">
                            <span class="text-white">in</span>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-[#dc2626] transition-colors">
                            <span class="text-white">ig</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 pt-8 text-center text-gray-400">
                <p>&copy; 2025 Obsidian Wraps & Signs. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scroll
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) target.scrollIntoView({ behavior: 'smooth', block: 'start' });
            });
        });

        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });

        // Portfolio filter
        const filterBtns = document.querySelectorAll('.filter-btn');
        const portfolioItems = document.querySelectorAll('.portfolio-item');
        
        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // Update button styles
                filterBtns.forEach(b => {
                    b.classList.remove('bg-[#dc2626]', 'text-white');
                    b.classList.add('bg-gray-800', 'text-gray-300');
                });
                this.classList.remove('bg-gray-800', 'text-gray-300');
                this.classList.add('bg-[#dc2626]', 'text-white');
                
                // Filter items
                portfolioItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-category') === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>

