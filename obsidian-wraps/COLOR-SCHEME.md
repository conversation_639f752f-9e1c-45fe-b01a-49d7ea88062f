# Obsidian Wraps & Signs - Color Scheme

## Current Color Palette

### Primary Colors

**Black** - Primary Color
- Hex: `#000000`
- RGB: `rgb(0, 0, 0)`
- Usage: Main text, headers, primary backgrounds

**Red** - Accent Color
- Hex: `#dc2626`
- RGB: `rgb(220, 38, 38)`
- Tailwind: `red-600`
- Usage: CTAs, highlights, hover states, brand accent

**Red Dark** - Accent Hover
- Hex: `#b91c1c`
- RGB: `rgb(185, 28, 28)`
- Tailwind: `red-700`
- Usage: Button hover states, active states

### Neutral Colors

**White** - Background
- Hex: `#ffffff`
- RGB: `rgb(255, 255, 255)`
- Usage: Main background, card backgrounds

**Gray** - Text & Borders
- Light Gray: `#f3f4f6` (gray-100)
- Medium Gray: `#6b7280` (gray-500)
- Dark Gray: `#1f2937` (gray-800)
- Usage: Secondary text, borders, subtle backgrounds

## Color Usage Guide

### Navigation
- Background: White with transparency
- Text: Gray-700
- Hover: Red (#dc2626)
- CTA Button: Red background, hover to Red-Dark

### Hero Section
- Background: Gray gradient (gray-50 to gray-100)
- Headline: Black
- Accent Text: Red
- Buttons: Red primary, White secondary

### Services Section
- Cards: White background
- Border: Gray-100, hover to Red
- Text: Black headings, Gray-600 body
- Hover: Red accent

### Portfolio Section
- Background: Gray-50
- Cards: White
- Overlay: Black gradient
- Tags: Red background

### Contact Section
- Background: Gray-900 (dark)
- Text: White
- Icons: Red background
- Form Focus: Red ring

### Footer
- Background: Black
- Text: White
- Links Hover: Red
- Accent Text: Red

## Accessibility

All color combinations meet WCAG 2.1 AA standards:

- **Black on White**: 21:1 (AAA)
- **Red on White**: 5.9:1 (AA)
- **White on Red**: 5.9:1 (AA)
- **White on Black**: 21:1 (AAA)
- **Gray-600 on White**: 5.7:1 (AA)

## Brand Guidelines

### Do's
✅ Use red for calls-to-action and important highlights
✅ Use black for primary text and headers
✅ Use gray for secondary text and subtle elements
✅ Maintain high contrast for readability
✅ Use white space generously

### Don'ts
❌ Don't use red for large text blocks
❌ Don't mix too many shades of gray
❌ Don't use low-contrast color combinations
❌ Don't overuse the accent color

## Quick Reference

```css
/* CSS Variables */
:root {
  --primary: #000000;      /* Black */
  --accent: #dc2626;       /* Red */
  --accent-dark: #b91c1c;  /* Red Dark */
  --gray: #6b7280;         /* Gray */
  --gray-light: #f3f4f6;   /* Light Gray */
  --gray-dark: #1f2937;    /* Dark Gray */
  --background: #ffffff;   /* White */
}
```

```javascript
// Tailwind Classes
const colors = {
  primary: 'text-black bg-black',
  accent: 'text-red-600 bg-red-600',
  accentHover: 'hover:text-red-700 hover:bg-red-700',
  gray: 'text-gray-500 bg-gray-500',
  grayLight: 'bg-gray-100',
  grayDark: 'bg-gray-800',
}
```

## Color Psychology

**Red** - Energy, passion, urgency, attention
- Perfect for CTAs and important actions
- Creates sense of excitement and boldness
- Stands out and demands attention

**Black** - Sophistication, power, elegance
- Professional and authoritative
- Creates strong contrast
- Timeless and classic

**White** - Cleanliness, simplicity, space
- Provides breathing room
- Enhances readability
- Modern and minimal

**Gray** - Balance, neutrality, professionalism
- Softens harsh contrasts
- Provides visual hierarchy
- Versatile and subtle

## Updating Colors

To change the color scheme:

1. Edit `app/globals.css` (lines 3-12)
2. Update CSS variables
3. Run find/replace in `app/page.tsx`:
   - Find: `#dc2626` (current red)
   - Replace: Your new accent color
   - Find: `#b91c1c` (current red-dark)
   - Replace: Your new accent hover color

4. Test contrast ratios at: https://webaim.org/resources/contrastchecker/

---

**Current Theme**: Bold & Professional (Black, Red, White, Gray)
**Last Updated**: 2025

