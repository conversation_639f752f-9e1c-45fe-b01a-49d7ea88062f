# Obsidian Wraps & Signs - Static Website Setup

## 🎯 What You Have

A **complete static frontend website** with no backend dependencies:
- Beautiful black/red/white/gray color scheme
- Fully responsive design (mobile, tablet, desktop)
- Interactive portfolio filtering
- Mobile navigation menu
- Smooth scrolling
- Contact form (frontend only)
- Professional animations and hover effects

## 🚀 How to Use

### Option 1: Open Directly (Recommended)
Simply **double-click `index.html`** or drag it into any web browser. That's it!

### Option 2: Local Server (Optional)
If you want to serve it locally:

**Python 3:**
```bash
cd obsidian-wraps
python -m http.server 8000
# Open http://localhost:8000
```

**Python 2:**
```bash
cd obsidian-wraps
python -m SimpleHTTPServer 8000
# Open http://localhost:8000
```

**Node.js (if you have it):**
```bash
cd obsidian-wraps
npx serve .
# Follow the URL shown
```

## 📁 File Structure

```
obsidian-wraps/
├── index.html           # 🎯 MAIN WEBSITE FILE (open this!)
├── public/              # Static assets
│   ├── file.svg
│   ├── globe.svg
│   ├── next.svg
│   ├── vercel.svg
│   └── window.svg
├── COLOR-SCHEME.md      # Color palette guide
├── QUICKSTART.txt       # Quick reference
└── SETUP.md            # This file
```

## 🎨 Customization

### Change Colors
Edit the color values in `index.html`:
- **Red**: `#dc2626` and `#b91c1c`
- **Black**: `#000000`
- **Gray**: `#1f2937`, `#374151`, `#6b7280`, etc.

### Change Content
All content is in `index.html`:
- **Line 40-50**: Navigation menu
- **Line 60-90**: Hero section
- **Line 95-140**: Services section
- **Line 145-200**: Portfolio section
- **Line 205-240**: Testimonials section
- **Line 245-290**: Contact section
- **Line 295-320**: Footer

### Add Images
1. Put your images in the `public/` folder
2. Update the `src` attributes in `index.html`
3. Use relative paths like `public/your-image.jpg`

## 🌐 Deployment

### Free Hosting Options
1. **Netlify**: Drag & drop the `obsidian-wraps` folder
2. **Vercel**: Upload the folder or connect to GitHub
3. **GitHub Pages**: Push to a GitHub repo and enable Pages
4. **Firebase Hosting**: Use `firebase deploy`

### What to Upload
Upload the entire `obsidian-wraps` folder containing:
- `index.html` (required)
- `public/` folder (optional, for icons)

## ✅ Features Included

- ✅ **Responsive Design** - Works on all devices
- ✅ **Dark Theme** - Black dominant with red accents
- ✅ **Interactive Portfolio** - Filter by category
- ✅ **Mobile Menu** - Hamburger navigation
- ✅ **Smooth Scrolling** - Professional navigation
- ✅ **Contact Form** - Ready for form handling service
- ✅ **SEO Ready** - Proper meta tags and structure
- ✅ **Fast Loading** - Optimized images and CSS
- ✅ **Cross-Browser** - Works in all modern browsers

## 🔧 Technical Details

- **Framework**: Pure HTML/CSS/JavaScript
- **Styling**: Tailwind CSS (via CDN)
- **Fonts**: Inter (Google Fonts)
- **Icons**: SVG icons and emojis
- **Images**: Unsplash stock photos (replace with your own)
- **No Build Process**: Ready to use immediately
- **No Dependencies**: Everything included

## 📞 Support

This is a complete static website ready for your signage business. Simply customize the content, add your images, and deploy!

**Your professional Obsidian Wraps & Signs website is ready to go! 🎊**
