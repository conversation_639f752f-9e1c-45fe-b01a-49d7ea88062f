# Setup Instructions for Obsidian Wraps & Signs Website

## 🎯 What You Have

A complete, modern, static website with:
- Beautiful responsive design
- Sticky navigation with mobile menu
- Hero section with statistics
- 6 services showcase
- Filterable portfolio gallery
- Customer testimonials
- Contact form
- Professional footer

## 🚀 To Run the Website

### Option 1: Local Development (Requires Node.js 20+)

```bash
# 1. Navigate to the project
cd obsidian-wraps

# 2. Install dependencies (first time only)
npm install

# 3. Start development server
npm run dev

# 4. Open browser to http://localhost:3000
```

### Option 2: If You Have Node.js 18 (Current Version)

The project requires Node.js 20.9.0+. To upgrade:

```bash
# Using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# Then run the website
cd obsidian-wraps
npm install
npm run dev
```

### Option 3: Deploy Without Running Locally

You can deploy directly to Vercel without running locally:

1. Push code to GitHub
2. Go to vercel.com
3. Import your repository
4. Vercel will automatically build and deploy

## 📝 Customization Checklist

### 1. Update Company Information

**File: `app/page.tsx`**

Line ~350 (Contact Section):
```typescript
- Phone: (*************
- Email: <EMAIL>  
- Address: 123 Business St, Your City, ST 12345
```

### 2. Add Your Services

**File: `app/page.tsx`** (Line ~7)

```typescript
const services = [
  {
    title: 'Your Service Name',
    description: 'Your service description',
    icon: '🚗', // Change emoji
  },
  // Add more services...
];
```

### 3. Add Your Projects

**File: `app/page.tsx`** (Line ~33)

```typescript
const portfolio = [
  {
    id: 1,
    category: 'vehicle', // or 'signage', 'building'
    title: 'Project Name',
    image: 'https://your-image-url.com/image.jpg'
  },
  // Add more projects...
];
```

**To use your own images:**
1. Add images to `public/` folder (e.g., `public/project1.jpg`)
2. Reference as: `image: '/project1.jpg'`

### 4. Add Customer Testimonials

**File: `app/page.tsx`** (Line ~42)

```typescript
const testimonials = [
  {
    name: 'Customer Name',
    company: 'Their Company',
    text: 'Their review text here',
    rating: 5, // 1-5 stars
  },
  // Add more testimonials...
];
```

### 5. Change Brand Colors

**File: `app/globals.css`** (Line ~3)

```css
:root {
  --accent: #e8e619;  /* Yellow - change to your brand color */
  --primary: #1a1a1a; /* Black - change to your primary color */
}
```

Then search and replace in `page.tsx`:
- `#e8e619` → Your accent color
- `#c4c416` → Your accent hover color

### 6. Update Page Title & Description

**File: `app/layout.tsx`** (Line ~11)

```typescript
export const metadata: Metadata = {
  title: "Your Company Name | Your Tagline",
  description: "Your company description for SEO",
};
```

### 7. Add Your Logo

1. Create/add logo image to `public/logo.png`
2. In `page.tsx` (Line ~75), replace text with:

```typescript
<img src="/logo.png" alt="Company Logo" className="h-12" />
```

## 🌐 Deployment Options

### Vercel (Recommended - Free)

1. Create account at vercel.com
2. Install Vercel CLI:
   ```bash
   npm i -g vercel
   ```
3. Deploy:
   ```bash
   cd obsidian-wraps
   vercel
   ```
4. Follow prompts
5. Your site will be live at: `your-project.vercel.app`

### Netlify (Free)

1. Create account at netlify.com
2. Drag and drop the `obsidian-wraps` folder
3. Or connect GitHub repository
4. Build settings:
   - Build command: `npm run build`
   - Publish directory: `.next`

### GitHub Pages (Free)

1. Add to `next.config.ts`:
   ```typescript
   output: 'export',
   images: { unoptimized: true }
   ```
2. Build:
   ```bash
   npm run build
   ```
3. Deploy `out` folder to GitHub Pages

## 📱 Testing Checklist

Before going live, test:

- [ ] All navigation links work
- [ ] Mobile menu opens/closes
- [ ] Portfolio filters work
- [ ] All images load
- [ ] Contact form fields work
- [ ] Responsive on mobile (use browser dev tools)
- [ ] Responsive on tablet
- [ ] All text is updated (no placeholder text)
- [ ] Contact information is correct
- [ ] Social media links work

## 🎨 Design Tips

1. **Images**: Use high-quality images (1200px+ width)
2. **Consistency**: Keep similar image aspect ratios
3. **Colors**: Stick to 2-3 main colors
4. **Text**: Keep descriptions concise and scannable
5. **CTAs**: Make buttons stand out with contrasting colors

## 🔧 Common Issues

**"Node.js version error"**
- Solution: Upgrade to Node.js 20+ (see Option 2 above)

**"Port 3000 already in use"**
- Solution: `npm run dev -- -p 3001`

**"Module not found"**
- Solution: `rm -rf node_modules && npm install`

**Images not loading**
- Check image URLs are correct
- For local images, ensure they're in `public/` folder
- Use `/image.jpg` not `./image.jpg`

## 📞 Need Help?

The website is fully functional and ready to customize. All the code is in:
- `app/page.tsx` - Main content
- `app/globals.css` - Styles
- `app/layout.tsx` - Site metadata

Everything is clearly commented and organized!

## ✅ What's Next?

1. Update all placeholder content
2. Add your real images
3. Test on mobile device
4. Deploy to Vercel/Netlify
5. Share your new website!

---

**Your website is ready to go! Just customize the content and deploy.** 🚀

