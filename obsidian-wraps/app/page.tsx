'use client';

import { useState } from 'react';

export default function Home() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');

  const services = [
    {
      title: 'Vehicle Wraps',
      description: 'Transform your vehicle into a mobile billboard with our premium vinyl wraps.',
      icon: '🚗',
    },
    {
      title: 'Custom Signage',
      description: 'Eye-catching signs that make your business stand out from the competition.',
      icon: '🪧',
    },
    {
      title: 'Shop Signage',
      description: 'Professional storefront signs that attract customers and build your brand.',
      icon: '🏪',
    },
    {
      title: 'Building Wraps',
      description: 'Large-scale building wraps for maximum visibility and impact.',
      icon: '🏢',
    },
    {
      title: 'Window Graphics',
      description: 'Frosted, printed, or cut vinyl graphics for windows and glass surfaces.',
      icon: '🪟',
    },
    {
      title: 'Fleet Branding',
      description: 'Consistent branding across your entire vehicle fleet.',
      icon: '🚚',
    },
  ];

  const portfolio = [
    { id: 1, category: 'vehicle', title: 'Luxury Car Wrap', image: 'https://images.unsplash.com/photo-1619405399517-d7fce0f13302?w=800&q=80' },
    { id: 2, category: 'signage', title: 'Retail Storefront', image: 'https://images.unsplash.com/photo-1556228578-0d85b1a4d571?w=800&q=80' },
    { id: 3, category: 'vehicle', title: 'Commercial Van', image: 'https://images.unsplash.com/photo-1527786356703-4b100091cd2c?w=800&q=80' },
    { id: 4, category: 'building', title: 'Building Wrap', image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&q=80' },
    { id: 5, category: 'signage', title: 'LED Sign', image: 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=800&q=80' },
    { id: 6, category: 'vehicle', title: 'Truck Graphics', image: 'https://images.unsplash.com/photo-1601584115197-04ecc0da31d7?w=800&q=80' },
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      company: 'Urban Cafe',
      text: 'Obsidian Wraps transformed our delivery van into a stunning mobile advertisement. The quality is exceptional!',
      rating: 5,
    },
    {
      name: 'Michael Chen',
      company: 'Tech Solutions Inc',
      text: 'Professional service from start to finish. Our new office signage looks incredible and really makes us stand out.',
      rating: 5,
    },
    {
      name: 'Emma Williams',
      company: 'Fitness First',
      text: 'The team at Obsidian Wraps exceeded our expectations. Our fleet looks amazing and the installation was flawless.',
      rating: 5,
    },
  ];

  const filteredPortfolio = selectedFilter === 'all'
    ? portfolio
    : portfolio.filter(item => item.category === selectedFilter);

  return (
    <div className="min-h-screen bg-black">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-black/95 backdrop-blur-sm z-50 border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-white">
                <span className="text-[#dc2626]">OBSIDIAN</span> WRAPS & SIGNS
              </h1>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#home" className="text-gray-300 hover:text-[#dc2626] transition-colors font-medium">Home</a>
              <a href="#services" className="text-gray-300 hover:text-[#dc2626] transition-colors font-medium">Services</a>
              <a href="#portfolio" className="text-gray-300 hover:text-[#dc2626] transition-colors font-medium">Portfolio</a>
              <a href="#testimonials" className="text-gray-300 hover:text-[#dc2626] transition-colors font-medium">Testimonials</a>
              <a href="#contact" className="bg-[#dc2626] text-white px-6 py-3 rounded-full font-semibold hover:bg-[#b91c1c] transition-all shadow-lg hover:shadow-xl">
                Get Quote
              </a>
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden p-2 rounded-md text-gray-300 hover:text-[#dc2626]"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {mobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t border-gray-800 bg-black">
            <div className="px-4 pt-2 pb-4 space-y-2">
              <a href="#home" className="block px-3 py-2 text-gray-300 hover:text-[#dc2626] font-medium">Home</a>
              <a href="#services" className="block px-3 py-2 text-gray-300 hover:text-[#dc2626] font-medium">Services</a>
              <a href="#portfolio" className="block px-3 py-2 text-gray-300 hover:text-[#dc2626] font-medium">Portfolio</a>
              <a href="#testimonials" className="block px-3 py-2 text-gray-300 hover:text-[#dc2626] font-medium">Testimonials</a>
              <a href="#contact" className="block px-3 py-2 bg-[#dc2626] text-white rounded-full font-semibold text-center">
                Get Quote
              </a>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-8 animate-fade-in">
              <h2 className="text-5xl md:text-6xl font-bold text-white leading-tight">
                Transform Your Brand with
                <span className="block text-[#dc2626]">Premium Wraps & Signs</span>
              </h2>
              <p className="text-xl text-gray-300 leading-relaxed">
                We create stunning vehicle wraps and custom signage that make your business impossible to ignore.
                Professional quality, competitive prices, exceptional service.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="#contact"
                  className="bg-[#dc2626] text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-[#b91c1c] transition-all shadow-lg hover:shadow-xl text-center"
                >
                  Get Free Quote
                </a>
                <a
                  href="#portfolio"
                  className="bg-gray-800 text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-700 transition-all border-2 border-gray-700 text-center"
                >
                  View Our Work
                </a>
              </div>
              <div className="flex items-center gap-8 pt-4">
                <div>
                  <div className="text-3xl font-bold text-white">25+</div>
                  <div className="text-sm text-gray-400">Years Experience</div>
                </div>
                <div className="h-12 w-px bg-gray-700"></div>
                <div>
                  <div className="text-3xl font-bold text-white">1000+</div>
                  <div className="text-sm text-gray-400">Projects Completed</div>
                </div>
                <div className="h-12 w-px bg-gray-700"></div>
                <div>
                  <div className="text-3xl font-bold text-white">100%</div>
                  <div className="text-sm text-gray-400">Satisfaction</div>
                </div>
              </div>
            </div>
            <div className="relative h-[500px] rounded-2xl overflow-hidden shadow-2xl border-2 border-gray-800">
              <img
                src="https://images.unsplash.com/photo-1619405399517-d7fce0f13302?w=1200&q=80"
                alt="Vehicle Wrap"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-900">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">Our Services</h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              We provide quality signage and wraps at competitive prices
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div
                key={index}
                className="group p-8 bg-black rounded-2xl border-2 border-gray-800 hover:border-[#dc2626] transition-all duration-300 hover:shadow-xl hover:shadow-red-900/20"
              >
                <div className="text-5xl mb-4">{service.icon}</div>
                <h3 className="text-2xl font-bold text-white mb-3 group-hover:text-[#dc2626] transition-colors">
                  {service.title}
                </h3>
                <p className="text-gray-400 leading-relaxed">
                  {service.description}
                </p>
                <button className="mt-6 text-[#dc2626] font-semibold hover:underline">
                  Learn More →
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-black">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">Our Work</h2>
            <p className="text-xl text-gray-300 mb-8">
              Recent projects that showcase our expertise
            </p>

            {/* Filter Buttons */}
            <div className="flex flex-wrap justify-center gap-3">
              <button
                onClick={() => setSelectedFilter('all')}
                className={`px-6 py-2 rounded-full font-semibold transition-all ${
                  selectedFilter === 'all'
                    ? 'bg-[#dc2626] text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                All
              </button>
              <button
                onClick={() => setSelectedFilter('vehicle')}
                className={`px-6 py-2 rounded-full font-semibold transition-all ${
                  selectedFilter === 'vehicle'
                    ? 'bg-[#dc2626] text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                Vehicle Wraps
              </button>
              <button
                onClick={() => setSelectedFilter('signage')}
                className={`px-6 py-2 rounded-full font-semibold transition-all ${
                  selectedFilter === 'signage'
                    ? 'bg-[#dc2626] text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                Signage
              </button>
              <button
                onClick={() => setSelectedFilter('building')}
                className={`px-6 py-2 rounded-full font-semibold transition-all ${
                  selectedFilter === 'building'
                    ? 'bg-[#dc2626] text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                Building Wraps
              </button>
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPortfolio.map((item) => (
              <div
                key={item.id}
                className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer"
              >
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <h3 className="text-white text-2xl font-bold mb-2">{item.title}</h3>
                    <span className="inline-block bg-[#dc2626] text-white px-3 py-1 rounded-full text-sm font-semibold">
                      {item.category}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <button className="bg-[#dc2626] text-white px-8 py-4 rounded-full font-bold hover:bg-[#b91c1c] transition-all shadow-lg">
              View All Projects
            </button>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-900">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">What Our Clients Say</h2>
            <p className="text-xl text-gray-300">
              Don't just take our word for it
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-black border-2 border-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-xl hover:border-[#dc2626] transition-all"
              >
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-[#dc2626]" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <p className="text-gray-300 mb-6 leading-relaxed italic">
                  "{testimonial.text}"
                </p>
                <div>
                  <div className="font-bold text-white">{testimonial.name}</div>
                  <div className="text-sm text-gray-400">{testimonial.company}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8 bg-black text-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold mb-6">Get Your Free Quote</h2>
              <p className="text-xl text-gray-300 mb-8">
                Ready to transform your brand? Contact us today for a free consultation and quote.
              </p>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="bg-[#dc2626] p-3 rounded-full">
                    <svg className="w-6 h-6 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-1">Phone</h3>
                    <p className="text-gray-300">(*************</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-[#dc2626] p-3 rounded-full">
                    <svg className="w-6 h-6 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-1">Email</h3>
                    <p className="text-gray-300"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-[#dc2626] p-3 rounded-full">
                    <svg className="w-6 h-6 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-1">Location</h3>
                    <p className="text-gray-300">123 Business St, Your City, ST 12345</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-900 border-2 border-gray-800 p-8 rounded-2xl">
              <form className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-semibold text-white mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    className="w-full px-4 py-3 rounded-lg border border-gray-700 bg-black text-white focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none placeholder-gray-500"
                    placeholder="John Doe"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-semibold text-white mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="w-full px-4 py-3 rounded-lg border border-gray-700 bg-black text-white focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none placeholder-gray-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-semibold text-white mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    className="w-full px-4 py-3 rounded-lg border border-gray-700 bg-black text-white focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none placeholder-gray-500"
                    placeholder="(*************"
                  />
                </div>

                <div>
                  <label htmlFor="service" className="block text-sm font-semibold text-white mb-2">
                    Service Interested In
                  </label>
                  <select
                    id="service"
                    className="w-full px-4 py-3 rounded-lg border border-gray-700 bg-black text-white focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none"
                  >
                    <option>Vehicle Wraps</option>
                    <option>Custom Signage</option>
                    <option>Shop Signage</option>
                    <option>Building Wraps</option>
                    <option>Window Graphics</option>
                    <option>Fleet Branding</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-semibold text-white mb-2">
                    Project Details
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    className="w-full px-4 py-3 rounded-lg border border-gray-700 bg-black text-white focus:ring-2 focus:ring-[#dc2626] focus:border-transparent outline-none resize-none placeholder-gray-500"
                    placeholder="Tell us about your project..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-[#dc2626] text-white py-4 rounded-lg font-bold text-lg hover:bg-[#b91c1c] transition-all shadow-lg hover:shadow-xl"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div>
              <h3 className="text-2xl font-bold mb-4">
                <span className="text-[#dc2626]">OBSIDIAN</span><br />WRAPS & SIGNS
              </h3>
              <p className="text-gray-400">
                Premium vehicle wraps and custom signage solutions for businesses of all sizes.
              </p>
            </div>

            <div>
              <h4 className="font-bold text-lg mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="#home" className="text-gray-400 hover:text-[#dc2626] transition-colors">Home</a></li>
                <li><a href="#services" className="text-gray-400 hover:text-[#dc2626] transition-colors">Services</a></li>
                <li><a href="#portfolio" className="text-gray-400 hover:text-[#dc2626] transition-colors">Portfolio</a></li>
                <li><a href="#testimonials" className="text-gray-400 hover:text-[#dc2626] transition-colors">Testimonials</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold text-lg mb-4">Services</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-[#dc2626] transition-colors">Vehicle Wraps</a></li>
                <li><a href="#" className="text-gray-400 hover:text-[#dc2626] transition-colors">Custom Signage</a></li>
                <li><a href="#" className="text-gray-400 hover:text-[#dc2626] transition-colors">Building Wraps</a></li>
                <li><a href="#" className="text-gray-400 hover:text-[#dc2626] transition-colors">Fleet Branding</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold text-lg mb-4">Follow Us</h4>
              <div className="flex gap-4">
                <a href="#" className="bg-gray-800 p-3 rounded-full hover:bg-[#dc2626] hover:text-gray-900 transition-all">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                <a href="#" className="bg-gray-800 p-3 rounded-full hover:bg-[#dc2626] hover:text-gray-900 transition-all">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                  </svg>
                </a>
                <a href="#" className="bg-gray-800 p-3 rounded-full hover:bg-[#dc2626] hover:text-gray-900 transition-all">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 text-center text-gray-400">
            <p>&copy; 2025 Obsidian Wraps & Signs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
