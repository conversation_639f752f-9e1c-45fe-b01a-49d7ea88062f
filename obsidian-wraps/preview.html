<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian Wraps & Signs - Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        html {
            scroll-behavior: smooth;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        ::-webkit-scrollbar {
            width: 10px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 5px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-white">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-white/95 backdrop-blur-sm z-50 border-b border-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">
                        <span class="text-[#e8e619]">OBSIDIAN</span> WRAPS & SIGNS
                    </h1>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-[#e8e619] transition-colors font-medium">Home</a>
                    <a href="#services" class="text-gray-700 hover:text-[#e8e619] transition-colors font-medium">Services</a>
                    <a href="#portfolio" class="text-gray-700 hover:text-[#e8e619] transition-colors font-medium">Portfolio</a>
                    <a href="#contact" class="bg-[#e8e619] text-gray-900 px-6 py-3 rounded-full font-semibold hover:bg-[#c4c416] transition-all shadow-lg">
                        Get Quote
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="pt-32 pb-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-gray-100">
        <div class="max-w-7xl mx-auto">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="space-y-8 animate-fade-in">
                    <h2 class="text-5xl md:text-6xl font-bold text-gray-900 leading-tight">
                        Transform Your Brand with
                        <span class="block text-[#e8e619]">Premium Wraps & Signs</span>
                    </h2>
                    <p class="text-xl text-gray-600 leading-relaxed">
                        We create stunning vehicle wraps and custom signage that make your business impossible to ignore.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="#contact" class="bg-[#e8e619] text-gray-900 px-8 py-4 rounded-full font-bold text-lg hover:bg-[#c4c416] transition-all shadow-lg text-center">
                            Get Free Quote
                        </a>
                        <a href="#portfolio" class="bg-white text-gray-900 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-50 transition-all border-2 border-gray-200 text-center">
                            View Our Work
                        </a>
                    </div>
                    <div class="flex items-center gap-8 pt-4">
                        <div>
                            <div class="text-3xl font-bold text-gray-900">25+</div>
                            <div class="text-sm text-gray-600">Years Experience</div>
                        </div>
                        <div class="h-12 w-px bg-gray-300"></div>
                        <div>
                            <div class="text-3xl font-bold text-gray-900">1000+</div>
                            <div class="text-sm text-gray-600">Projects</div>
                        </div>
                        <div class="h-12 w-px bg-gray-300"></div>
                        <div>
                            <div class="text-3xl font-bold text-gray-900">100%</div>
                            <div class="text-sm text-gray-600">Satisfaction</div>
                        </div>
                    </div>
                </div>
                <div class="relative h-[500px] rounded-2xl overflow-hidden shadow-2xl">
                    <img src="https://images.unsplash.com/photo-1619405399517-d7fce0f13302?w=1200&q=80" alt="Vehicle Wrap" class="w-full h-full object-cover">
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Services</h2>
                <p class="text-xl text-gray-600">Quality signage and wraps at competitive prices</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-100 hover:border-[#e8e619] transition-all duration-300 hover:shadow-xl">
                    <div class="text-5xl mb-4">🚗</div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-[#e8e619] transition-colors">Vehicle Wraps</h3>
                    <p class="text-gray-600">Transform your vehicle into a mobile billboard with our premium vinyl wraps.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-100 hover:border-[#e8e619] transition-all duration-300 hover:shadow-xl">
                    <div class="text-5xl mb-4">🪧</div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-[#e8e619] transition-colors">Custom Signage</h3>
                    <p class="text-gray-600">Eye-catching signs that make your business stand out from the competition.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-100 hover:border-[#e8e619] transition-all duration-300 hover:shadow-xl">
                    <div class="text-5xl mb-4">🏪</div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-[#e8e619] transition-colors">Shop Signage</h3>
                    <p class="text-gray-600">Professional storefront signs that attract customers and build your brand.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-100 hover:border-[#e8e619] transition-all duration-300 hover:shadow-xl">
                    <div class="text-5xl mb-4">🏢</div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-[#e8e619] transition-colors">Building Wraps</h3>
                    <p class="text-gray-600">Large-scale building wraps for maximum visibility and impact.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-100 hover:border-[#e8e619] transition-all duration-300 hover:shadow-xl">
                    <div class="text-5xl mb-4">🪟</div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-[#e8e619] transition-colors">Window Graphics</h3>
                    <p class="text-gray-600">Frosted, printed, or cut vinyl graphics for windows and glass surfaces.</p>
                </div>
                <div class="group p-8 bg-white rounded-2xl border-2 border-gray-100 hover:border-[#e8e619] transition-all duration-300 hover:shadow-xl">
                    <div class="text-5xl mb-4">🚚</div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-[#e8e619] transition-colors">Fleet Branding</h3>
                    <p class="text-gray-600">Consistent branding across your entire vehicle fleet.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Work</h2>
                <p class="text-xl text-gray-600">Recent projects that showcase our expertise</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300">
                    <img src="https://images.unsplash.com/photo-1619405399517-d7fce0f13302?w=800&q=80" alt="Project" class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-white text-2xl font-bold mb-2">Luxury Car Wrap</h3>
                            <span class="inline-block bg-[#e8e619] text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">Vehicle</span>
                        </div>
                    </div>
                </div>
                <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300">
                    <img src="https://images.unsplash.com/photo-1556228578-0d85b1a4d571?w=800&q=80" alt="Project" class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-white text-2xl font-bold mb-2">Retail Storefront</h3>
                            <span class="inline-block bg-[#e8e619] text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">Signage</span>
                        </div>
                    </div>
                </div>
                <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300">
                    <img src="https://images.unsplash.com/photo-1527786356703-4b100091cd2c?w=800&q=80" alt="Project" class="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div class="absolute bottom-0 left-0 right-0 p-6">
                            <h3 class="text-white text-2xl font-bold mb-2">Commercial Van</h3>
                            <span class="inline-block bg-[#e8e619] text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">Vehicle</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 px-4 sm:px-6 lg:px-8 bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto text-center">
            <h2 class="text-4xl md:text-5xl font-bold mb-6">Get Your Free Quote</h2>
            <p class="text-xl text-gray-300 mb-8">Ready to transform your brand? Contact us today!</p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <div class="flex items-center gap-3">
                    <div class="bg-[#e8e619] p-3 rounded-full">
                        <svg class="w-6 h-6 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                    </div>
                    <div class="text-left">
                        <div class="font-bold">Phone</div>
                        <div class="text-gray-300">(*************</div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="bg-[#e8e619] p-3 rounded-full">
                        <svg class="w-6 h-6 text-gray-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="text-left">
                        <div class="font-bold">Email</div>
                        <div class="text-gray-300"><EMAIL></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black text-white py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto text-center">
            <h3 class="text-2xl font-bold mb-4">
                <span class="text-[#e8e619]">OBSIDIAN</span> WRAPS & SIGNS
            </h3>
            <p class="text-gray-400 mb-8">Premium vehicle wraps and custom signage solutions</p>
            <p class="text-gray-400">&copy; 2025 Obsidian Wraps & Signs. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Smooth scroll for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });
        });
    </script>
</body>
</html>

